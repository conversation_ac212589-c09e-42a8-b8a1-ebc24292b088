# Timer Sound Implementation Plan

## Overview
This document outlines the plan for incorporating sound notifications into the Noti app's timer functionality, including configurable settings to enable/disable sounds for different timer events.

## Current Architecture Analysis

### Timer System Components
- **Timer Store** (`src/stores/timerStore.ts`): Core timer state management with Pinia
- **Pomodoro Timer Component** (`src/components/timer/PomodoroTimer.vue`): Main UI component
- **Timer Settings Modal** (`src/components/timer/TimerSettingsModal.vue`): Configuration interface
- **Timer API** (`electron/main/api/timer-api.ts`): Backend timer operations
- **Settings Store** (`src/stores/settingsStore.ts`): Application-wide settings management

### Key Integration Points
1. **Timer Completion Handler** (`timerStore.ts:466`): `handleTimerComplete()` function
2. **Timer Settings System**: Existing modal and database schema for timer preferences
3. **Settings Architecture**: Established pattern for app-wide settings with database persistence

## Audio Implementation Options Analysis

### Option 1: HTML5 Audio API (Recommended)
**Pros:**
- Simple implementation with `<audio>` elements
- Good browser support and Electron compatibility
- Lightweight and performant
- Easy to preload sounds for instant playback

**Cons:**
- Subject to autoplay policies (mitigated in Electron)
- Limited advanced audio features

**Implementation:**
```javascript
const audio = new Audio('/sounds/timer-complete.mp3');
audio.preload = 'auto';
audio.play().catch(error => console.error('Audio playback failed:', error));
```

### Option 2: Web Audio API
**Pros:**
- Advanced audio control and effects
- Better performance for complex audio scenarios
- Precise timing control

**Cons:**
- More complex implementation
- Overkill for simple notification sounds
- Larger learning curve

### Option 3: Native Electron Audio
**Pros:**
- System-level audio integration
- Bypasses web audio limitations

**Cons:**
- Platform-specific implementation required
- More complex setup and maintenance

**Recommendation:** HTML5 Audio API for simplicity and reliability.

## Sound Settings Architecture Design

### Database Schema Extension
Add sound-related fields to existing `app_settings` table:

```sql
-- Add to existing app_settings table
ALTER TABLE app_settings ADD COLUMN timer_sounds_enabled BOOLEAN DEFAULT 1;
ALTER TABLE app_settings ADD COLUMN pomodoro_complete_sound TEXT DEFAULT 'default';
ALTER TABLE app_settings ADD COLUMN break_complete_sound TEXT DEFAULT 'default';
ALTER TABLE app_settings ADD COLUMN timer_start_sound TEXT DEFAULT 'none';
ALTER TABLE app_settings ADD COLUMN sound_volume REAL DEFAULT 0.7;
```

### Settings Store Integration
Extend `AppSettings` interface in `src/stores/settingsStore.ts`:

```typescript
export interface AppSettings {
  // ... existing settings
  
  // Timer sound settings
  timerSoundsEnabled: boolean
  pomodoroCompleteSound: string
  breakCompleteSound: string
  timerStartSound: string
  soundVolume: number
}
```

### Settings UI Component
Create new `TimerSoundSettings.vue` component or extend existing `TimerSettingsModal.vue`:

```vue
<div class="form-section">
  <h4 class="section-title">Sound Notifications</h4>
  <div class="checkbox-field">
    <input type="checkbox" v-model="settings.timerSoundsEnabled" />
    <label>Enable timer sounds</label>
  </div>
  <div v-if="settings.timerSoundsEnabled" class="sound-settings">
    <div class="form-field">
      <label>Pomodoro Complete Sound</label>
      <select v-model="settings.pomodoroCompleteSound">
        <option value="default">Default Bell</option>
        <option value="chime">Soft Chime</option>
        <option value="ding">Classic Ding</option>
        <option value="none">No Sound</option>
      </select>
    </div>
    <!-- Similar for other sound types -->
    <div class="form-field">
      <label>Volume</label>
      <input type="range" min="0" max="1" step="0.1" v-model="settings.soundVolume" />
    </div>
  </div>
</div>
```

## Audio File Management Plan

### File Storage Location
- **Development**: `public/sounds/` directory
- **Production**: Bundled with app in `resources/sounds/`
- **Custom Sounds**: User data directory for future extensibility

### Supported Audio Formats
- **Primary**: MP3 (universal support, good compression)
- **Secondary**: WAV (uncompressed, higher quality)
- **Fallback**: OGG (open source alternative)

### Default Sound Library
Curate a small collection of professional timer sounds:
1. **default-bell.mp3** - Classic notification bell
2. **soft-chime.mp3** - Gentle chime for breaks
3. **work-complete.mp3** - Satisfying completion sound
4. **break-start.mp3** - Relaxing break start sound

### File Size Considerations
- Keep individual files under 100KB
- Total sound library under 500KB
- Use appropriate compression settings

## Implementation Plan

### Phase 1: Core Audio System
1. Create audio service/composable (`src/composables/useAudio.ts`)
2. Implement basic sound playback functionality
3. Add sound preloading for performance
4. Handle audio playback errors gracefully

### Phase 2: Settings Integration
1. Extend database schema for sound settings
2. Update settings store with sound preferences
3. Create/extend settings UI components
4. Implement settings persistence

### Phase 3: Timer Integration
1. Integrate audio service into timer store
2. Add sound triggers to timer completion handlers
3. Implement volume control and muting
4. Add sound preview functionality in settings

### Phase 4: Audio Assets
1. Source or create default sound files
2. Optimize audio files for size and quality
3. Implement audio file bundling for production
4. Add fallback handling for missing files

### Phase 5: Testing & Polish
1. Test across different operating systems
2. Verify audio works in both dev and production builds
3. Implement user feedback for sound preferences
4. Add accessibility considerations

## Technical Implementation Details

### Audio Service Structure
```typescript
// src/composables/useAudio.ts
export interface AudioService {
  playSound(soundId: string, volume?: number): Promise<void>
  preloadSounds(): Promise<void>
  setGlobalVolume(volume: number): void
  isEnabled(): boolean
  setEnabled(enabled: boolean): void
}
```

### Timer Integration Points
1. **Pomodoro Complete**: `handleTimerComplete()` when `timerType === 'pomodoro'`
2. **Break Complete**: `handleTimerComplete()` when `timerType` includes 'Break'
3. **Timer Start**: `toggleTimer()` when starting timer (optional)
4. **Session Complete**: End of full pomodoro session (optional)

### Error Handling Strategy
- Graceful degradation when audio fails
- User notification for audio issues
- Fallback to visual notifications only
- Logging for debugging audio problems

## Future Enhancements
- Custom sound file upload
- Sound themes/packs
- Advanced audio effects
- Integration with system notifications
- Accessibility features (visual alternatives)

## Estimated Implementation Time
- **Phase 1-2**: 4-6 hours (core system + settings)
- **Phase 3**: 2-3 hours (timer integration)
- **Phase 4**: 2-3 hours (audio assets)
- **Phase 5**: 2-4 hours (testing & polish)
- **Total**: 10-16 hours

## Dependencies
- No new external dependencies required
- Uses existing Electron and Vue.js capabilities
- Leverages current settings and timer architecture

## Detailed Code Implementation Examples

### 1. Audio Service Composable (`src/composables/useAudio.ts`)

```typescript
import { ref, computed } from 'vue'
import { useSettingsStore } from '../stores/settingsStore'

interface SoundMap {
  [key: string]: HTMLAudioElement
}

export function useAudio() {
  const settingsStore = useSettingsStore()
  const sounds = ref<SoundMap>({})
  const isLoading = ref(false)
  const loadError = ref<string | null>(null)

  // Sound file mappings
  const soundFiles = {
    'pomodoro-complete': '/sounds/pomodoro-complete.mp3',
    'break-complete': '/sounds/break-complete.mp3',
    'timer-start': '/sounds/timer-start.mp3',
    'session-complete': '/sounds/session-complete.mp3'
  }

  // Computed properties
  const isEnabled = computed(() => settingsStore.settings.timerSoundsEnabled)
  const globalVolume = computed(() => settingsStore.settings.soundVolume)

  // Preload all sound files
  async function preloadSounds(): Promise<void> {
    if (isLoading.value) return

    isLoading.value = true
    loadError.value = null

    try {
      const loadPromises = Object.entries(soundFiles).map(([key, path]) => {
        return new Promise<void>((resolve, reject) => {
          const audio = new Audio(path)
          audio.preload = 'auto'
          audio.volume = globalVolume.value

          audio.addEventListener('canplaythrough', () => {
            sounds.value[key] = audio
            resolve()
          })

          audio.addEventListener('error', (e) => {
            console.warn(`Failed to load sound: ${key}`, e)
            // Don't reject - allow app to continue without this sound
            resolve()
          })
        })
      })

      await Promise.all(loadPromises)
      console.log('✅ Audio preloading completed')
    } catch (error) {
      loadError.value = 'Failed to load audio files'
      console.error('❌ Audio preloading failed:', error)
    } finally {
      isLoading.value = false
    }
  }

  // Play a specific sound
  async function playSound(soundId: string, volume?: number): Promise<void> {
    if (!isEnabled.value) {
      console.log('🔇 Audio disabled, skipping sound:', soundId)
      return
    }

    const audio = sounds.value[soundId]
    if (!audio) {
      console.warn('🔊 Sound not found:', soundId)
      return
    }

    try {
      // Reset audio to beginning
      audio.currentTime = 0

      // Set volume (use provided volume or global setting)
      audio.volume = volume !== undefined ? volume : globalVolume.value

      // Play the sound
      await audio.play()
      console.log('🔊 Played sound:', soundId)
    } catch (error) {
      console.error('❌ Failed to play sound:', soundId, error)
    }
  }

  // Update volume for all loaded sounds
  function updateGlobalVolume(volume: number): void {
    Object.values(sounds.value).forEach(audio => {
      audio.volume = volume
    })
  }

  // Test a sound (for settings preview)
  async function testSound(soundId: string): Promise<void> {
    await playSound(soundId, globalVolume.value)
  }

  return {
    // State
    isLoading: readonly(isLoading),
    loadError: readonly(loadError),
    isEnabled,

    // Methods
    preloadSounds,
    playSound,
    testSound,
    updateGlobalVolume
  }
}
```

### 2. Timer Store Integration

```typescript
// Add to src/stores/timerStore.ts
import { useAudio } from '../composables/useAudio'

export const useTimerStore = defineStore('timer', () => {
    // ... existing code ...

    // Initialize audio service
    const audio = useAudio()

    // Initialize audio on store creation
    onMounted(() => {
        audio.preloadSounds()
    })

    // Modified handleTimerComplete function
    async function handleTimerComplete() {
        console.log('🏁 [TimerStore] handleTimerComplete called for type:', state.value.timerType)

        // Play completion sound based on timer type
        if (state.value.timerType === 'pomodoro') {
            await audio.playSound('pomodoro-complete')
            console.log('🎯 [TimerStore] Completing pomodoro cycle in database...')
            // ... existing pomodoro completion logic ...
        } else if (state.value.timerType === 'shortBreak' || state.value.timerType === 'longBreak') {
            await audio.playSound('break-complete')
            console.log('☕ [TimerStore] Completing break cycle...')
            // ... existing break completion logic ...
        }

        // ... rest of existing completion logic ...
    }

    // Modified toggleTimer function to play start sound
    async function toggleTimer(): Promise<{ success: boolean; reason?: string }> {
        // ... existing validation logic ...

        if (!state.value.isRunning) {
            // Starting timer - play start sound
            await audio.playSound('timer-start')
            // ... existing start logic ...
        } else {
            // Pausing timer - no sound needed
            // ... existing pause logic ...
        }

        // ... rest of existing toggle logic ...
    }

    return {
        // ... existing exports ...
        audio // Export audio service for component access
    }
})
```

### 3. Settings Store Extension

```typescript
// Add to src/stores/settingsStore.ts
export interface AppSettings {
  // ... existing settings ...

  // Timer sound settings
  timerSoundsEnabled: boolean
  pomodoroCompleteSound: string
  breakCompleteSound: string
  timerStartSound: string
  soundVolume: number
}

// Update default settings
const settings = ref<AppSettings>({
    // ... existing defaults ...

    // Sound defaults
    timerSoundsEnabled: true,
    pomodoroCompleteSound: 'default',
    breakCompleteSound: 'default',
    timerStartSound: 'none',
    soundVolume: 0.7
})

// Update resetToDefaults function
async function resetToDefaults() {
    const defaultSettings: AppSettings = {
        // ... existing defaults ...

        // Sound defaults
        timerSoundsEnabled: true,
        pomodoroCompleteSound: 'default',
        breakCompleteSound: 'default',
        timerStartSound: 'none',
        soundVolume: 0.7
    }
    // ... rest of reset logic ...
}
```

### 4. Timer Settings Modal Extension

```vue
<!-- Add to src/components/timer/TimerSettingsModal.vue -->
<template>
  <!-- ... existing template ... -->

  <!-- Add new Sound Settings section -->
  <div class="form-section">
    <h4 class="section-title">Sound Notifications</h4>
    <div class="form-grid">
      <div class="checkbox-field">
        <div class="checkbox-wrapper">
          <input
            type="checkbox"
            v-model="settings.timerSoundsEnabled"
            class="form-checkbox"
            id="timer-sounds-enabled"
          />
          <label for="timer-sounds-enabled" class="checkbox-label">
            <span class="checkbox-title">Enable Timer Sounds</span>
            <span class="checkbox-description">Play notification sounds when timers complete</span>
          </label>
        </div>
      </div>

      <div v-if="settings.timerSoundsEnabled" class="sound-settings">
        <div class="form-field">
          <label>Pomodoro Complete Sound</label>
          <div class="sound-selector">
            <select v-model="settings.pomodoroCompleteSound" class="form-select">
              <option value="default">Default Bell</option>
              <option value="chime">Soft Chime</option>
              <option value="ding">Classic Ding</option>
              <option value="none">No Sound</option>
            </select>
            <button @click="testSound('pomodoro-complete')" class="btn btn-test">Test</button>
          </div>
        </div>

        <div class="form-field">
          <label>Break Complete Sound</label>
          <div class="sound-selector">
            <select v-model="settings.breakCompleteSound" class="form-select">
              <option value="default">Default Bell</option>
              <option value="chime">Soft Chime</option>
              <option value="ding">Classic Ding</option>
              <option value="none">No Sound</option>
            </select>
            <button @click="testSound('break-complete')" class="btn btn-test">Test</button>
          </div>
        </div>

        <div class="form-field">
          <label>Volume</label>
          <div class="volume-control">
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              v-model.number="settings.soundVolume"
              class="volume-slider"
            />
            <span class="volume-display">{{ Math.round(settings.soundVolume * 100) }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// Add to existing script section
import { useAudio } from '../../composables/useAudio'

export default defineComponent({
  setup(props, { emit }) {
    // ... existing setup ...

    const audio = useAudio()

    // Test sound function
    const testSound = async (soundId: string) => {
      await audio.testSound(soundId)
    }

    // Watch volume changes and update audio
    watch(() => settings.soundVolume, (newVolume) => {
      audio.updateGlobalVolume(newVolume)
    })

    return {
      // ... existing returns ...
      testSound
    }
  }
})
</script>

<style scoped>
.sound-settings {
  margin-top: 1rem;
  padding-left: 1.5rem;
  border-left: 2px solid var(--border-color);
}

.sound-selector {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-test {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-test:hover {
  background: var(--accent-color-hover);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.volume-slider {
  flex: 1;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  outline: none;
}

.volume-display {
  min-width: 3rem;
  text-align: right;
  font-size: 0.875rem;
  color: var(--text-secondary);
}
</style>
```

## Database Migration Script

```sql
-- Add sound settings to app_settings table
-- This should be added to the database initialization or migration script

ALTER TABLE app_settings ADD COLUMN timer_sounds_enabled BOOLEAN DEFAULT 1;
ALTER TABLE app_settings ADD COLUMN pomodoro_complete_sound TEXT DEFAULT 'default';
ALTER TABLE app_settings ADD COLUMN break_complete_sound TEXT DEFAULT 'default';
ALTER TABLE app_settings ADD COLUMN timer_start_sound TEXT DEFAULT 'none';
ALTER TABLE app_settings ADD COLUMN sound_volume REAL DEFAULT 0.7;
```
